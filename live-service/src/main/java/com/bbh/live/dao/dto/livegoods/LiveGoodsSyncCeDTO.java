package com.bbh.live.dao.dto.livegoods;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/8/27
 * @Description:
 */
@Data
public class LiveGoodsSyncCeDTO {

    private Long liveRoomId;

    private List<LiveGoodsSyncCe> liveGoods;

    @Data
    public static class LiveGoodsSyncCe{
        private Long liveGoodsId;
        private BigDecimal peerPrice;
    }
}
