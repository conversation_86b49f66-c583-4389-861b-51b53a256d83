package com.bbh.live.dao.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.AbortiveAuctionGoodsDTO;
import com.bbh.live.dao.dto.CheckedLiveGoodsDTO;
import com.bbh.live.dao.dto.OffhandPutAwayDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSaleDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsStatisticsDTO;
import com.bbh.live.dao.dto.vo.AuctionLiveGoodsVO;
import com.bbh.live.service.msg.dto.base.BaseGoods;
import com.bbh.model.LiveGoods;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【live_goods(直播间商品)】的数据库操作Service
 * @createDate 2024-07-25 11:53:19
 */
public interface LiveGoodsService extends IService<LiveGoods> {

    /**
     * 初始化一条新的直播商品对象
     *
     * @param liveRoomId    直播间id
     * @param globalGoodsId
     * @return
     */
    LiveGoods initializeNewLiveGoods(Long liveRoomId, Long globalGoodsId);

    /**
     *
     * @param liveGoodsIdList
     * @return 不存在的直播商品id集合
     */
    List<Long> checkLiveGoodsExists(List<Long> liveGoodsIdList);

    /**
     * 获取直播间商品最大商品编码 用于新增商品的编码
     *
     * @param liveRoomId
     * @return
     */
    Long getMaxLiveGoodsCodeByLiveRoomId(Long liveRoomId);


    /**
     * 取直播间商品最小商品排序号 用于修改商品排序
     * @param liveRoomId
     * @return
     */
    Integer getMinSortGoodsByLiveRoomId(Long liveRoomId);

    /**
     * 取直播间商品最大商品排序号 用于修改商品排序
     * @param liveRoomId
     * @return
     */
    Integer getMaxSortGoodsByLiveRoomId(Long liveRoomId);

    /**
     * 清单列表
     *
     * @param queryReq
     * @return
     */
    IPage<LiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq queryReq);

    /**
     * 获取待完善商品列表
     * @param queryReq
     * @return
     */
    IPage<LiveGoodsDTO> getWaitCompleteLiveGoodsList(LiveGoodsQueryReq queryReq);

    /**
     * 查询商品详情
     *
     * @param liveGoodsId
     * @return
     */
    LiveGoodsDTO getLiveGoodsDetailInfo(Long liveGoodsId);


    /**
     * 查询商品详情, 发消息使用
     *
     * @param liveGoodsId
     * @return
     */
    BaseGoods getBaseGoodsInfo(Long liveGoodsId);

    /**
     * 批量更新商品排序
     *
     * @param sortDTOList
     */
    void batchUpdateLiveGoodsSort(List<LiveGoodsSortDTO> sortDTOList);

    /**
     * 批量上云展检查 并返回 有用的 清单商品列表
     *
     * @param liveGoodsIds 清单商品Id
     * @return 有效的 能转移的 商品列表
     */
    List<LiveGoods> batchToCeLogicCheckAndReturnErpGoodsList(List<Long> liveGoodsIds);


    /**
     * 获取指定直播间流拍商品id列表
     * @param liveRoomIdList
     * @param fetchSize 查询的数量
     * @return
     */
    List<AbortiveAuctionGoodsDTO> getAuctionFailLiveGoodsListByLiveRoomId(Collection<Long> liveRoomIdList, Integer fetchSize);


    /**
     * 直播商品数量统计，分为
     *      直播前： 直播前只统计待完善和全部、待完善 指 商品状态为待上架但直播三要素（起拍价，加价幅度，竞拍时长）未填写的商品
     *      直播后： 直播开始后统计 待上架、已成交、流拍商品数量
     * @param queryReq
     */
    LiveGoodsStatisticsDTO getLiveGoodsCountBeforeLiveStarted(LiveGoodsQueryReq queryReq);


    /**
     * 直播商品数量统计
     * 直播后： 直播开始后统计 待上架、已成交、流拍商品数量
     * @param queryReq
     * @return
     */
    LiveGoodsStatisticsDTO getLiveGoodsCountAfterLiveStarted(LiveGoodsQueryReq queryReq);

    /**
     * 商品预约记录增加 |减少
     * @param liveGoodsId
     * @param delta
     */
    void incrGoodsSubscribeCount(Long liveGoodsId, int delta);

    /**
     * 获取已选择的商品列表
     * @param liveRoomId
     * @return
     */
    List<CheckedLiveGoodsDTO> getCheckedLiveGoodsList(Long liveRoomId);

    /**
     * 获取直播间商品列表
     * @param liveRoomIds
     * @return
     */
    List<AuctionLiveGoodsVO> getAuctionLiveGoodsListByLiveRoom(List<Long> liveRoomIds);

    /**
     * 获取直播间上架讲解中的商品列表
     * @param liveRoomIds
     * @return
     */
    List<AuctionLiveGoodsVO> getPutAwayLiveGoodsListByLiveRoom(List<Long> liveRoomIds);

    /**
     * 创建即拍即上商品
     * @param offhandPutAwayDTO
     * @param globalGoodsId
     * @return
     */
    LiveGoods createOffhandLiveGoods(OffhandPutAwayDTO offhandPutAwayDTO, Long globalGoodsId);

    /**
     * 已售出商品列表
     * @param liveRoomId
     */
    List<LiveGoodsSaleDTO> getSoldGoodsListByLiveRoom(Long liveRoomId);

    /**
     * 获取直播间流拍商品数量
     * @param liveRoomIdList
     * @return
     */
    Map<Long, Long> getEffectiveAuctionFailLiveGoodsCount(Collection<Long> liveRoomIdList);

}
