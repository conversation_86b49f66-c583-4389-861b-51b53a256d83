package com.bbh.live.dao.dto.livegoods;

import com.bbh.enums.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/25
 * @Description: 直播商品清单DTO
 */
@Data
@Accessors(chain = true)
public class LiveGoodsDTO {

    private Long id;

    private Long liveGoodsId;

    /**
     * 商户id
     */
    private Long orgId;

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 拍号，唯一且固定不变
     */
    private String liveGoodsCode;

    /**
     * ERP商品ID
     */
    private Long globalGoodsId;

    /**
     * 成交类型
     */
    private LiveGoodsTradeTypeEnum tradeType;

    /**
     * ERP的删除时间
     */
    private Date erpDeletedAt;

    /**
     * 货品编码
     */
    private String globalGoodsCode;

    /**
     * 货品名称
     */
    private String globalGoodsName;

    /**
     * 商品描述
     */
    private String globalGoodsDescription;

    /**
     * 货品图片 示例 ["https://bang-file","https://bang-file"]
     */
    private List<String> imgUrlList;

    /**
     * 成色 例如 N、S 、A
     */
    private String quality;

    /**
     * erp是否开单 0否1是
     */
    private Integer placeOrderStatus;

    /**
     * 成本价格
     */
    private BigDecimal costPrice;

    /**
     * 同行价格
     */
    private BigDecimal peerPrice;

    /**
     * 竞拍中当前价格
     */
    private BigDecimal currentPrice;

    /**
     * 竞拍到期时间
     */
    private Date auctionEndTime;

    /**
     * 商品状态: 00-待完善, 10-待上架, 20-上架中, 30-竞拍中, 40-已流拍, 50-已成交, 60-已下架
     */
    private LiveGoodsStatusEnum goodsStatus;

    /**
     * 后台取消状态: 0 未取消  10 待审核 20 审核通过 30 驳回
     */
    private LiveGoodsCancelStatusEnum cancelStatus;

    /**
     * 结算状态: 00-待结算, 20-已结算
     */
    private Integer settleStatus;

    /**
     * 起拍价
     */
    private BigDecimal startPrice = BigDecimal.ZERO;

    /**
     * 加价幅度
     */
    private BigDecimal increasePrice = BigDecimal.ZERO;

    /**
     * 竞拍时长，上架时设置
     */
    private Integer auctionDuration = 0;

    /**
     * 实际竞拍时长
     */
    private Integer actualAuctionDuration;

    /**
     * 上架时间
     */
    private Date putawayAt;

    /**
     * 竞拍开始时间
     */
    private Date auctionStartAt;

    /**
     * 商品结束时间，含流拍、成交、下架
     */
    private Date endAt;

    /**
     * 售出价格
     */
    private BigDecimal sellPrice;

    /**
     * 成交人
     */
    private Long belongUserId;

    /**
     * 成交人席位
     */
    private Long belongSeatId;

    /**
     * 成交人席位，默认取昵称，没有就取席位名称
     */
    private String belongSeatName;

    /**
     * 成交人拍号
     */
    private String belongSeatAuctionCode;

    /**
     * 买手商户id
     */
    private Long belongOrgId;

    /**
     * 成交方式
     */
    private LiveGoodsBelongTypeEnum belongType;

    /**
     * 商品排序号: 初始化时值为拍号
     */
    private Integer sort;

    /**
     * 预约感兴趣数量
     */
    private Integer subscribeCount;

    /**
     * 导播备注，仅导播和主播可见
     */
    private String directorRemark;

    /**
     * 传送记录id
     */
    private Long transferRecordId;

    /**
     * 是否传送中
     */
    private Boolean isTransfer;

    /**
     * 是否传送中
     */
    private BigDecimal transferPrice;

    /**
     * 传送人席位id
     */
    private Long transferSeatId;

    /**
     * erp是否售出
     */
    private Integer erpSaleStatus;

    /**
     * erp是否开单
     */
    private Boolean erpPlaceOrderStatus;

    /**
     * erp是否锁单
     */
    private Boolean erpIfLocked;

    /**
     * 一级分类id，通过连表live_spu_classify查询到的一级分类ID，这个表是中台写进来的
     */
    private Integer oneClassifyId;

    /**
     * 平台分类id，现在不再从spu去匹配，直接用商品清单表的平台分类id
     */
    private Integer platformClassifyId;

    /**
     * SPU的ID，ERP表的SPU_ID
     */
    private String spuId;

    /**
     * 直播间的买手服务费率，仅生成订单时使用
     */
    private BigDecimal liveRoomBuyerServiceRate = BigDecimal.ZERO;
    /** 买家取消成交的状态 */
    private LiveGoodsBuyerCancelRecordStatusEnum buyerCancelStatus;
    /**
     * 是否同步到云展，等同于是否有回放，仅同步到云展的商品才有回放
     */
    private Boolean ifSyncCe;

    /**
     * 直播回放地址
     */
    private String liveVideoUrl;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建人席位
     */
    private Long createSeatId;

    /**
     * 更新人
     */
    private Long updateUserId;

    /**
     * 更新人席位
     */
    private Long updateSeatId;
}
