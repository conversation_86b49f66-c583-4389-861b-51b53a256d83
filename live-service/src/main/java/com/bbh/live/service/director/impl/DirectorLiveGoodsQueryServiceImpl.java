package com.bbh.live.service.director.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bbh.base.ListBase;
import com.bbh.base.Sort;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.CheckedLiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsCheckListDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsStatisticsDTO;
import com.bbh.live.dao.service.LiveGoodsService;
import com.bbh.live.service.director.DirectorLiveGoodsQueryService;
import com.bbh.live.service.livegoods.LiveGoodsDetailService;
import com.bbh.live.service.livegoods.LiveGoodsListService;
import com.bbh.live.service.room.LiveRoomBizService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.live.util.LivePermissionChecker;
import com.bbh.model.LiveGoods;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Service
@AllArgsConstructor
public class DirectorLiveGoodsQueryServiceImpl implements DirectorLiveGoodsQueryService {

    private final LiveGoodsListService liveGoodsListService;
    private final LiveGoodsDetailService liveGoodsDetailService;
    private final LiveGoodsService liveGoodsService;
    private final LiveRoomBizService liveRoomService;

    @Override
    public ListBase<LiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq liveGoodsQueryReq) {
        IPage<LiveGoodsDTO> goodsPage = liveGoodsListService.getLiveGoodsList(liveGoodsQueryReq);
        return ListBase.pageConvertToListBase(goodsPage);
    }

    @Override
    public ListBase<LiveGoodsDTO> getLiveGoodsSyncToSceSelectAll(LiveGoodsQueryReq liveGoodsQueryReq) {
        liveGoodsQueryReq.setPerPage(Integer.MAX_VALUE);
        IPage<LiveGoodsDTO> goodsPage = liveGoodsListService.getLiveGoodsList(liveGoodsQueryReq);
        List<LiveGoodsDTO> filterList = goodsPage.getRecords().stream().filter(x ->x.getIsTransfer()==null|| Boolean.FALSE.equals(x.getIsTransfer())).collect(Collectors.toList());
        goodsPage.setRecords(filterList);
        return ListBase.pageConvertToListBase(goodsPage);
    }

    @Override
    public ListBase<CheckedLiveGoodsDTO> getCheckedLiveGoodsList() {
        var checkedLiveGoodsList = liveGoodsService.getCheckedLiveGoodsList(LiveRoomContextHolder.getRoomId());
        return new ListBase<>(checkedLiveGoodsList, 0L, 0L, 0L, null);
    }

    @Override
    public ListBase<LiveGoodsDTO> getWaitPutAwayLiveGoodsList(Long liveRoomId) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());
        LiveGoodsQueryReq query = new LiveGoodsQueryReq()
                .setLiveRoomId(liveRoomId)
                .setLiveGoodsStatusList(List.of(LiveGoodsStatusEnum.WAIT_PUT_AWAY.getCode()))
                .setFilterLockedOrSoldOutGoods(true)
                .setSort(Sort.of(LiveGoodsConstant.DEFAULT_SORT_FIELD, Sort.Direction.ASC));
        return ListBase.pageConvertToListBase(liveGoodsListService.getLiveGoodsList(query));
    }

    @Override
    public LiveGoodsDTO getPutAwayLiveGoodsDetail(Long liveRoomId) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());
        //查询当前直播间正在上架讲解或在竞拍中的商品，一个直播间只能有一个商品处于上架讲解和竞拍中
        LambdaQueryChainWrapper<LiveGoods> query = liveGoodsService.lambdaQuery().eq(LiveGoods::getLiveRoomId, liveRoomId)
                .in(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.PUT_AWAY.getCode(), LiveGoodsStatusEnum.AUCTION.getCode())
                .select(LiveGoods::getId);
        LiveGoods putAwayOrInAuctionLiveGoods = liveGoodsService.getOne(query);
        if(putAwayOrInAuctionLiveGoods == null){
            return null;
        }

        return liveGoodsDetailService.getLiveGoodsDetailInfo(putAwayOrInAuctionLiveGoods.getId());
    }

    @Override
    public LiveGoodsCheckListDTO getLiveGoodsCheckList(LiveGoodsQueryReq queryReq) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveGoodsCheckListDTO liveGoodsCheckList = new LiveGoodsCheckListDTO();

        // 统计信息
        LiveGoodsStatisticsDTO liveGoodsStatistics = liveGoodsListService.getDirectorLiveGoodsCount(queryReq);
        liveGoodsCheckList
                .setWaitCompleteCount(liveGoodsStatistics.getWaitCompleteCount())
                .setWaitPutAwayCount(liveGoodsStatistics.getWaitPutAwayCount())
                .setAbortiveAuctionCount(liveGoodsStatistics.getAbortiveAuctionCount())
                .setTradeCount(liveGoodsStatistics.getTradeCount())
                .setTotalCount(liveGoodsStatistics.getTotalCount());
        // 商品列表
        IPage<LiveGoodsDTO> goodsList = liveGoodsListService.getLiveGoodsList(queryReq);
        liveGoodsCheckList.setLiveGoodsList(goodsList.getRecords());

        // 商品是待完善或待上架状态，取决于直播是否已开始。直播前待完善状态是指直播三要素未完成的商品（起拍价，加价幅度，竞拍时长）
        liveGoodsCheckList.setIsLiveRoomStarted(LiveRoomContextHolder.liveRoomIsStarted());
        liveGoodsCheckList.setRoomStatus(liveRoomService.computedLiveRoomEnhancedStatus(LiveRoomContextHolder.getRoomId()));
        return liveGoodsCheckList;
    }

    @Override
    public LiveGoodsCheckListDTO getGoodsCount(LiveGoodsQueryReq queryReq) {
        //导播权限校验
        LivePermissionChecker.assertDirector(LiveRoomContextHolder.getLiveRoomContext().getRoomId());

        LiveGoodsCheckListDTO liveGoodsCheckList = new LiveGoodsCheckListDTO();

        // 统计信息
        LiveGoodsStatisticsDTO liveGoodsStatistics = liveGoodsListService.getDirectorLiveGoodsCount(queryReq);
        liveGoodsCheckList
                .setWaitCompleteCount(liveGoodsStatistics.getWaitCompleteCount())
                .setWaitPutAwayCount(liveGoodsStatistics.getWaitPutAwayCount())
                .setAbortiveAuctionCount(liveGoodsStatistics.getAbortiveAuctionCount())
                .setTradeCount(liveGoodsStatistics.getTradeCount())
                .setTotalCount(liveGoodsStatistics.getTotalCount());
        liveGoodsCheckList.setIsLiveRoomStarted(LiveRoomContextHolder.liveRoomIsStarted());

        return liveGoodsCheckList;
    }
}
