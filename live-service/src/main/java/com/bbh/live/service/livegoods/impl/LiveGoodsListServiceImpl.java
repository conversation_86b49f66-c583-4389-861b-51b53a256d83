package com.bbh.live.service.livegoods.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbh.enums.LiveGoodsStatusEnum;
import com.bbh.live.constant.LiveGoodsConstant;
import com.bbh.live.controller.req.LiveGoodsQueryReq;
import com.bbh.live.dao.dto.AbortiveAuctionGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsSortDTO;
import com.bbh.live.dao.dto.livegoods.LiveGoodsStatisticsDTO;
import com.bbh.live.dao.dto.vo.AuctionLiveGoodsVO;
import com.bbh.live.dao.service.LiveGoodsTransferService;
import com.bbh.live.service.livegoods.LiveGoodsListService;
import com.bbh.live.service.msg.MsgService;
import com.bbh.live.service.room.context.LiveRoomContextHolder;
import com.bbh.model.ErpGoods;
import com.bbh.model.LiveGoods;
import com.bbh.model.LiveGoodsTransfer;
import com.bbh.util.AssertUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dsy
 * @Date: 2024/7/26
 * @Description:
 */
@Service
public class LiveGoodsListServiceImpl extends AbstractLiveGoodsService implements LiveGoodsListService {

    @Resource
    protected LiveGoodsTransferService liveGoodsTransferService;
    @Resource
    private MsgService msgService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addLiveGoodsListFromErpStorehouse(Long liveRoomId, List<Long> globalGoodsIdList) {
        if(CollectionUtil.isEmpty(globalGoodsIdList)){
            return 0;
        }
        //校验erp商品是否存在
        List<ErpGoods> existsErpGoodsList = erpGoodsService.checkErpGoodsExists(globalGoodsIdList);
        Map<Long, ErpGoods> erpGoodsMap = existsErpGoodsList.stream().collect(Collectors.toMap(ErpGoods::getId, Function.identity()));
        //当前直播商品最大编号
        Long maxLiveGoodsCode = liveGoodsService.getMaxLiveGoodsCodeByLiveRoomId(liveRoomId);
        //当前直播商品最大排序号 值越小排序越靠前
        Integer maxLiveGoodsSort = liveGoodsService.getMaxSortGoodsByLiveRoomId(liveRoomId);

        List<LiveGoods> liveGoodsList = new ArrayList<>();
        for (Long globalGoodsId : erpGoodsMap.keySet()) {
            LiveGoods liveGoods = liveGoodsService.lambdaQuery().eq(LiveGoods::getGlobalGoodsId, globalGoodsId).eq(LiveGoods::getLiveRoomId, liveRoomId).one();
            if(liveGoods == null){
                liveGoods = liveGoodsService.initializeNewLiveGoods(liveRoomId, globalGoodsId);
                liveGoods.setAuctionDuration(liveServiceProperties.getDefaultAuctionDuration());
                liveGoods.setIncreasePrice(BigDecimal.valueOf(liveServiceProperties.getDefaultIncreasePrice()));
            }
            liveGoods.setLiveGoodsCode(++maxLiveGoodsCode);
            liveGoods.setSort(Math.toIntExact(++maxLiveGoodsSort));
            liveGoodsList.add(liveGoods);
        }
        // 批量新增
        liveGoodsService.saveOrUpdateBatch(liveGoodsList);

        // 发送新增消息
        msgService.goodsInsert(liveRoomId, liveGoodsList.stream().map(LiveGoods::getId).collect(Collectors.toList()));

        return liveGoodsList.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeLiveGoodsList(List<Long> liveGoodsIdList) {

        //检查指定商品是否存在
        List<Long> notExsitsLiveGoodsIdList = liveGoodsService.checkLiveGoodsExists(liveGoodsIdList);
        AssertUtil.assertTrue(notExsitsLiveGoodsIdList.isEmpty(), "商品不存在,商品id：" + notExsitsLiveGoodsIdList);

        //只有待上架且未传送的商品才能删除
        // 待上架校验
        List<Long> waitPutawayLiveGoodsIdList = liveGoodsService.lambdaQuery()
                .in(LiveGoods::getId, liveGoodsIdList)
                .eq(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.WAIT_PUT_AWAY.getCode())
                .select(LiveGoods::getId)
                .list().stream().map(LiveGoods::getId)
                .toList();
        AssertUtil.assertEquals(waitPutawayLiveGoodsIdList.size(), liveGoodsIdList.size(), "商品不是待上架，无法删除");

        // 传送校验
        List<Long> transferLiveGoodsIdList = liveGoodsTransferService.lambdaQuery()
                .eq(LiveGoodsTransfer::getLiveRoomId, LiveRoomContextHolder.getRoomId())
                .in(LiveGoodsTransfer::getLiveGoodsId, liveGoodsIdList)
                .eq(LiveGoodsTransfer::getIfHandled, false)
                .gt(LiveGoodsTransfer::getExpireAt, new Date())
                .select(LiveGoodsTransfer::getLiveGoodsId)
                .list().stream().map(LiveGoodsTransfer::getLiveGoodsId)
                .toList();
        AssertUtil.assertTrue(transferLiveGoodsIdList.isEmpty(), "商品正在传送，无法删除");

        LambdaQueryWrapper<LiveGoods> wrapper = Wrappers.lambdaQuery(LiveGoods.class)
                .eq(LiveGoods::getLiveRoomId, LiveRoomContextHolder.getRoomId())
                .in(LiveGoods::getId, liveGoodsIdList);
        return liveGoodsService.getBaseMapper().delete(wrapper);
    }

    @Override
    public IPage<LiveGoodsDTO> getLiveGoodsList(LiveGoodsQueryReq queryReq) {
        var roomId = queryReq.getLiveRoomId();
        AssertUtil.assertNotNull(roomId, "直播间id不能为空");

        var liveGoodsStatusList = queryReq.getLiveGoodsStatusList();

        IPage<LiveGoodsDTO> liveGoodsPage;
        //只查询待完善商品，待完善商品=商品状态为待上架但上架三要素为空的商品
        if(CollectionUtil.isNotEmpty(liveGoodsStatusList) && liveGoodsStatusList.size() == 1 && liveGoodsStatusList.contains(LiveGoodsStatusEnum.WAIT_COMPLETE.getCode())){
            liveGoodsPage = liveGoodsService.getWaitCompleteLiveGoodsList(queryReq);
        } else {
            liveGoodsPage = liveGoodsService.getLiveGoodsList(queryReq);
        }

        if(liveGoodsPage.getRecords().isEmpty()){
            return liveGoodsPage;
        }
        //完善当前价格等信息。这些信息在直播预约时已放入缓存中
        super.richLiveGoods(liveGoodsPage.getRecords());
        return liveGoodsPage;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateLiveGoodsSortValue(List<LiveGoodsSortDTO> sortDTOList) {
        //加上排序起始值
        sortDTOList.forEach(sortDTO -> sortDTO.setSort(sortDTO.getSort() + LiveGoodsConstant.SORT_BEGIN_VALUE));
        liveGoodsService.batchUpdateLiveGoodsSort(sortDTOList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLiveGoodsSortAsWaitPutAwayFirst(Long liveGoodsId) {
        LiveGoods first = liveGoodsService.getById(liveGoodsId);
        AssertUtil.assertNotNull(first, "商品不存在");

        Long liveRoomId = first.getLiveRoomId();
        //当前排序第一的商品排序号
        Integer minSort = liveGoodsService.getMinSortGoodsByLiveRoomId(liveRoomId);
        //已经是第一位
        if(minSort.equals(first.getSort())){
            return;
        }
        first.setSort(minSort - 1);
        liveGoodsService.updateById(first);
    }

    @Override
    public List<AbortiveAuctionGoodsDTO> getAuctionFailLiveGoodsListByLiveRoomId(Collection<Long> liveRoomIdList, Integer fetchSize) {
        return liveGoodsService.getAuctionFailLiveGoodsListByLiveRoomId(liveRoomIdList, fetchSize);
    }

    @Override
    public Map<Long, Long> getEffectiveAuctionFailLiveGoodsCount(Collection<Long> liveRoomIdList) {
        return liveGoodsService.getEffectiveAuctionFailLiveGoodsCount(liveRoomIdList);
    }

    @Override
    public LiveGoodsStatisticsDTO getDirectorLiveGoodsCount(LiveGoodsQueryReq queryReq) {
        var roomId = queryReq.getLiveRoomId();
        AssertUtil.assertNotNull(roomId, "直播间id不能为空");
        // 商品是待完善或待上架状态，取决于直播是否已开始。直播前待完善状态是指直播三要素未完成的商品（起拍价，加价幅度，竞拍时长）
        if(LiveRoomContextHolder.liveRoomIsStarted()){
            return liveGoodsService.getLiveGoodsCountAfterLiveStarted(queryReq);
        }else{
            return liveGoodsService.getLiveGoodsCountBeforeLiveStarted(queryReq);
        }
    }

    @Override
    public LiveGoodsStatisticsDTO getBuyerLiveGoodsCount(LiveGoodsQueryReq queryReq) {
        var roomId = queryReq.getLiveRoomId();
        AssertUtil.assertNotNull(roomId, "直播间id不能为空");
        return liveGoodsService.getLiveGoodsCountAfterLiveStarted(queryReq);
    }

    @Override
    public Long getAuctionFailLiveGoodsCount(Long liveRoomId) {
        return liveGoodsService.lambdaQuery().eq(LiveGoods::getLiveRoomId, liveRoomId)
                .eq(LiveGoods::getGoodsStatus, LiveGoodsStatusEnum.ABORTIVE_AUCTION.getCode())
                .count();
    }

    @Override
    public List<AuctionLiveGoodsVO> getAuctionLiveGoodsListByLiveRoom(List<Long> liveRoomIds) {
        if(CollectionUtil.isEmpty(liveRoomIds)){
            return null;
        }
        // 所有正在竞拍中的商品
        List<AuctionLiveGoodsVO> auctionLiveGoodsList = liveGoodsService.getAuctionLiveGoodsListByLiveRoom(liveRoomIds);

        var iterator = auctionLiveGoodsList.iterator();
        while (iterator.hasNext()){
            var goods = iterator.next();
            // 竞拍信息
            var auctionLiveGoods = liveGoodsCacheManager.getLiveGoodsAuctionCache().getAuctionLiveGoodsBeforeExpire(goods.getLiveRoomId(), goods.getLiveGoodsId());
            if(auctionLiveGoods == null){
                // 缓存无竞拍信息 可能是脏数据
                iterator.remove();
            }else {
                goods.setCurrentPrice(auctionLiveGoods.getCurrentPrice());
                goods.setBuyerSeatId(auctionLiveGoods.getBuyerSeatId());
                goods.setRemainTime(DateUtil.between(DateUtil.date(), auctionLiveGoods.getAuctionEndTime(), DateUnit.SECOND, false));
                goods.setRemainTimeMs(DateUtil.between(DateUtil.date(), auctionLiveGoods.getAuctionEndTime(), DateUnit.MS));
            }
        }
        return auctionLiveGoodsList;
    }

    @Override
    public List<AuctionLiveGoodsVO> getPutAwayLiveGoodsListByLiveRoom(List<Long> liveRoomIds) {
        if(CollectionUtil.isEmpty(liveRoomIds)){
            return null;
        }
        return liveGoodsService.getPutAwayLiveGoodsListByLiveRoom(liveRoomIds);
    }
}
