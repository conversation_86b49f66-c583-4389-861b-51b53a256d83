package com.bbh.live.service.virutalgoods.processor;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bbh.enums.VipBuyerClickRecordTypeEnum;
import com.bbh.exception.ServiceException;
import com.bbh.live.dao.service.IGlobalOrganizationService;
import com.bbh.live.dao.service.VipBuyerClickRecordService;
import com.bbh.live.feign.dto.GlobalOrgDetailDTO;
import com.bbh.live.service.buyer.vip.BuyerVipService;
import com.bbh.live.service.permission.PermissionService;
import com.bbh.model.*;
import com.bbh.vo.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR> dsy
 * @Date: 2024/9/12
 * @Description: 买家会员订单处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuyerVipOrderProcessor implements IVirtualOrderProcessor {

    private final BuyerVipService buyerVipService;
    private final PermissionService permissionService;
    private final VipBuyerClickRecordService vipBuyerClickRecordService;
    private final IGlobalOrganizationService globalOrganizationService;


    /**
     * 前置处理方法，用于拦截器校验
     *
     * @param buyerSeat    买手信息
     * @param virtualGoods 虚拟商品信息
     * @return 是否允许继续执行
     */
    @Override
    public boolean preProcess(GlobalOrgSeat buyerSeat, GlobalOrderOtherGoodsDic virtualGoods) {
        boolean isAuth = permissionService.isAuth(buyerSeat.getOrgId());
        if (!isAuth) {
            throw new ServiceException("未认证，请先完成认证");
        }
        return true;
    }

    @Override
    public Result process(VirtualOrderProcessContext context) {
        GlobalVirtualGoodsOrder order = context.getOrder();
        // 购买人席位
        var buyerSeatId = order.getBuyerSeatId();

        // 购买的会员商品
        GlobalOrderOtherGoodsDic virtualGoods = context.getGoods();
        if (virtualGoods == null) {
            return Result.fail("商品信息不能为空");
        }
        // 年费会员和月费会员，只能通过goodsNumber来区分
        boolean ifYear = (virtualGoods.getGoodsNumber() == 12);

        // 新开通或者续费会员
        Long vipCardId = buyerVipService.activateAndRenewVipCard(order.getId());

/*        // 找到埋点记录，然后更新支付类型
        updatePageClickRecord(vipCardId, buyerSeatId, ifYear);
        updatePayClickRecord(vipCardId, buyerSeatId, ifYear);*/
        //取最新的点击记录，另存为新的购买记录
        try {
        addNewVipBuyerClickRecord(context.getOrder().getBuyerSeatId(),ifYear,order);
        } catch (Exception e) {
            log.error("添加会员购买记录失败",e);
        }
        return Result.ok();
    }

    private void addNewVipBuyerClickRecord(Long buyerSeatId, boolean ifYear,GlobalVirtualGoodsOrder order) {
        VipBuyerClickRecord vipBuyerClickRecord=vipBuyerClickRecordService.getOne(
                new LambdaQueryWrapper<VipBuyerClickRecord>()
                        .eq(VipBuyerClickRecord::getSeatId,buyerSeatId)
                        // 根据创建时间取最新的一条
                        .orderByDesc(VipBuyerClickRecord::getCreatedAt)
                        .last("limit 1")
        );
        if (vipBuyerClickRecord!=null){
            VipBuyerClickRecord addVipBuyerClickRecord=new VipBuyerClickRecord();
            BeanUtil.copyProperties(vipBuyerClickRecord,addVipBuyerClickRecord);
            addVipBuyerClickRecord.setId(null);
            //数据类型 1 浏览点击 2 开通点击
            addVipBuyerClickRecord.setDataType(2);
            //是否付费成功 0否 1 是
            addVipBuyerClickRecord.setPayType(1);
            addVipBuyerClickRecord.setPayTime(new Date());
            addVipBuyerClickRecord.setVipType(ifYear ? 2 : 1);
            vipBuyerClickRecordService.save(addVipBuyerClickRecord);
        }else{
            GlobalOrgDetailDTO orgDetailById = globalOrganizationService.getOrgDetailById(order.getOrgId());

            VipBuyerClickRecord addVipBuyerClickRecord=new VipBuyerClickRecord();
            //数据类型 1 浏览点击 2 开通点击
            addVipBuyerClickRecord.setDataType(2);
            //是否付费成功 0否 1 是
            addVipBuyerClickRecord.setPayType(1);
            addVipBuyerClickRecord.setPayTime(new Date());
            addVipBuyerClickRecord.setVipType(ifYear ? 2 : 1);
            addVipBuyerClickRecord.setOrgId(order.getOrgId());
            addVipBuyerClickRecord.setOrgName(order.getOrgName());
            addVipBuyerClickRecord.setOrgType(orgDetailById.getType());
            addVipBuyerClickRecord.setPhone(orgDetailById.getPurePhone());
            addVipBuyerClickRecord.setOrgPhone(orgDetailById.getPhone());
            addVipBuyerClickRecord.setUserId(Math.toIntExact(order.getBuyerUserId()));
            addVipBuyerClickRecord.setSeatId(buyerSeatId);
            addVipBuyerClickRecord.setSourceType(VipBuyerClickRecordTypeEnum.LIVE);
            vipBuyerClickRecordService.save(addVipBuyerClickRecord);
        }

    }

    private void updatePageClickRecord(Long vipCardId, Long buyerSeatId, boolean ifYear) {
        vipBuyerClickRecordService.lambdaQuery()
                // 购买人
                .eq(VipBuyerClickRecord::getSeatId, buyerSeatId)
                // 类型 1-点击仅会员页面 2-点击开通会员按钮
                .eq(VipBuyerClickRecord::getType, 1)
                // 根据创建时间取最新的一条
                .orderByDesc(VipBuyerClickRecord::getCreatedAt).last("limit 1").oneOpt().ifPresent(
                        record -> vipBuyerClickRecordService.lambdaUpdate().eq(VipBuyerClickRecord::getId, record.getId())
                                // 更新支付时间
                                .set(VipBuyerClickRecord::getPayTime, new Date())
                                // 是否付费成功 0否 1 是（月费vip） 2是（年费vip）
                                .set(VipBuyerClickRecord::getPayType, ifYear ? 2 : 1)
                                // 更新会员卡id
                                .set(VipBuyerClickRecord::getVipBuyerCardId, vipCardId == null ? 0 : vipCardId).update());
    }

    private void updatePayClickRecord(Long vipCardId, Long buyerSeatId, boolean ifYear) {
        vipBuyerClickRecordService.lambdaQuery()
                // 购买人
                .eq(VipBuyerClickRecord::getSeatId, buyerSeatId)
                // 类型 1-点击仅会员页面 2-点击开通会员按钮
                .eq(VipBuyerClickRecord::getType, 2)
                // 根据创建时间取最新的一条
                .orderByDesc(VipBuyerClickRecord::getCreatedAt).last("limit 1").oneOpt().ifPresent(
                        record -> vipBuyerClickRecordService.lambdaUpdate().eq(VipBuyerClickRecord::getId, record.getId())
                                // 更新支付时间
                                .set(VipBuyerClickRecord::getPayTime, new Date())
                                // 是否付费成功 0否 1 是（月费vip） 2是（年费vip）
                                .set(VipBuyerClickRecord::getPayType, ifYear ? 2 : 1)
                                // 更新会员卡id
                                .set(VipBuyerClickRecord::getVipBuyerCardId, vipCardId == null ? 0 : vipCardId).update());
    }
}
